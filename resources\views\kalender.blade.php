<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kalender PT. Putera Wibowo Borneo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    @vite([
        'resources/css/app.css',
        'resources/css/calender.css',
        'resources/js/app.js'
    ])
</head>
<style>
    html {
        background: none;
    }

    body {
        padding: 2%;
        display: flex;
        flex-direction: column;
        /* min-height: 100%; */
        /* full viewport height */
        background-image:
            linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
            url('{{ asset('images/bg.png') }}');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
    }

    .header {
        flex-shrink: 0;
    }
</style>

<body>

    <div class="page-container">
        <div class="row">
            <div class="col-4 flex items-center justify-center">
                <div class="month-navigation flex items-center space-x-4">
                    <span class="arrow text-2xl cursor-pointer">&lt;</span>
                    <h2 class="text-large font-bold">AGUSTUS 2025</h2>
                    <span class="arrow text-2xl cursor-pointer">&gt;</span>
                </div>
            </div>
            <div class="col-8">
                <div class="div1 header">
                    <div class="right-logo">
                        <div class="logo-line">
                            <img src="{{ asset('images/logo.png') }}" alt="Logo" />
                            <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
                        </div>
                        <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <main class="calendar-layout mt-10">
            <section class="main-calendar-panel">
                <div class="main-calendar-grid">
                    <div class="day-name">Minggu</div>
                    <div class="day-name">Senin</div>
                    <div class="day-name">Selasa</div>
                    <div class="day-name">Rabu</div>
                    <div class="day-name">Kamis</div>
                    <div class="day-name">Jumat</div>
                    <div class="day-name">Sabtu</div>

                    <div class="date-cell inactive" data-date="29" data-month="07">29</div>
                    <div class="date-cell inactive" data-date="30" data-month="07">30</div>
                    <div class="date-cell clickable" data-date="01" data-month="08">01</div>
                    <div class="date-cell clickable" data-date="02" data-month="08">02</div>
                    <div class="date-cell clickable" data-date="03" data-month="08">03</div>
                    <div class="date-cell clickable" data-date="04" data-month="08">04</div>
                    <div class="date-cell clickable" data-date="05" data-month="08">05</div>

                    <div class="date-cell sunday clickable" data-date="06" data-month="08">06</div>
                    <div class="date-cell clickable" data-date="07" data-month="08">07</div>
                    <div class="date-cell clickable" data-date="08" data-month="08">08</div>
                    <div class="date-cell clickable" data-date="09" data-month="08">09</div>
                    <div class="date-cell clickable" data-date="10" data-month="08">10</div>
                    <div class="date-cell clickable" data-date="11" data-month="08">11</div>
                    <div class="date-cell clickable" data-date="12" data-month="08">12</div>

                    <div class="date-cell sunday clickable" data-date="13" data-month="08">13</div>
                    <div class="date-cell clickable" data-date="14" data-month="08">14</div>
                    <div class="date-cell clickable" data-date="15" data-month="08">15</div>
                    <div class="date-cell clickable" data-date="16" data-month="08">16</div>
                    <div class="date-cell holiday clickable" data-date="17" data-month="08">17</div>
                    <div class="date-cell clickable" data-date="18" data-month="08">18</div>
                    <div class="date-cell clickable" data-date="19" data-month="08">19</div>

                    <div class="date-cell sunday clickable" data-date="20" data-month="08">20</div>
                    <div class="date-cell clickable" data-date="21" data-month="08">21</div>
                    <div class="date-cell clickable" data-date="22" data-month="08">22</div>
                    <div class="date-cell clickable" data-date="23" data-month="08">23</div>
                    <div class="date-cell clickable" data-date="24" data-month="08">24</div>
                    <div class="date-cell highlighted clickable" data-date="25" data-month="08">25</div>
                    <div class="date-cell clickable" data-date="26" data-month="08">26</div>

                    <div class="date-cell sunday clickable" data-date="27" data-month="08">27</div>
                    <div class="date-cell clickable" data-date="28" data-month="08">28</div>
                    <div class="date-cell clickable" data-date="29" data-month="08">29</div>
                    <div class="date-cell clickable" data-date="30" data-month="08">30</div>
                    <div class="date-cell clickable" data-date="31" data-month="08">31</div>
                    <div class="date-cell inactive" data-date="01" data-month="09">01</div>
                    <div class="date-cell inactive" data-date="02" data-month="09">02</div>
                </div>
                <p class="holiday-note">17 Agustus 2025 : Hari Kemerdekaan Republik Indonesia</p>

                <!-- Activity Display Section -->
                <div id="activityDisplay" class="activity-display" style="display: none;">
                    <div class="activity-header">
                        <h3 id="selectedDateTitle">Kegiatan untuk tanggal</h3>
                        <button id="closeActivityBtn" class="close-activity-btn">&times;</button>
                    </div>
                    <div id="activityList" class="activity-list">
                        <!-- Activities will be populated here by JavaScript -->
                    </div>
                </div>
            </section>

            <aside class="side-panel">
                <div class="mini-calendar">
                    <h3>Juli 2025</h3>
                    <div class="mini-grid">
                        <span>M</span><span>S</span><span>S</span><span>R</span><span>K</span><span>J</span><span>S</span>
                        <span class="inactive">29</span><span
                            class="inactive">30</span><span>01</span><span>02</span><span>03</span><span>04</span><span
                            class="sunday">05</span>
                        <span
                            class="sunday">06</span><span>07</span><span>08</span><span>09</span><span>10</span><span>11</span><span>12</span>
                        <span
                            class="sunday">13</span><span>14</span><span>15</span><span>16</span><span>17</span><span>18</span><span>19</span>
                        <span
                            class="sunday">20</span><span>21</span><span>22</span><span>23</span><span>24</span><span>25</span><span>26</span>
                        <span class="sunday">27</span><span>28</span><span>29</span><span>30</span><span>31</span><span
                            class="inactive">01</span><span class="inactive">02</span>
                    </div>
                </div>

                <div class="mini-calendar">
                    <div class="mini-calendar max-w-md mx-auto">
                        <!-- Container semua reminder form -->
                        <div id="reminderForms">
                            <!-- Form pertama -->
                            <form class="reminder-form space-y-4 p-4 rounded-lg shadow mb-4 dark:bg-gray-800">
                                <p>Pengingat Tugas/Kegiatan</p>
                                <div>
                                    <input type="text" name="activityName" class="bg-transparent  w-full px-3 py-2 border rounded-md text-sm text-dark" placeholder="Masukkan Kegiatan" required />
                                </div>
                                <div>
                                    <input type="date" name="activityDate"
                                        class="bg-transparent  w-full px-3 py-2 border rounded-md text-sm text-dark"
                                        required />
                                </div>
                                <div>
                                    <div class="flex justify-end mb-3">
                                        <input type="time" name="activityTime"
                                            class="bg-transparent  w-full px-3 py-2 border rounded-md text-sm text-dark"
                                            required />
                                        <button id="addReminderBtn"
                                            class="text-white bg-blue-400 hover:bg-blue-800 px-4 py-2 rounded-lg text-lg font-bold ml-10">+</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </aside>

        </main>

        <a href="{{ route('home') }}" class="home-button neumorphism">
            <img class="imgicon-purple" src="{{ asset('assets/icon/home.png') }}" alt="Home" width="40" height="40" >
            </img>
            <span>HOME</span>
        </a>
    </div>

    <!-- Enhanced Calendar Styles -->
    <style>
        /* Enhanced date cell styling for better visual hierarchy */
        .date-cell {
            font-size: 1.4rem;
            font-weight: 500;
            color: var(--dark-blue);
            padding: 0.6rem;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 55px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .date-cell.clickable {
            cursor: pointer;
            user-select: none;
        }

        .date-cell.clickable:hover {
            background-color: rgba(108, 86, 123, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .date-cell.clickable:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .date-cell.selected {
            background-color: var(--purple);
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(108, 86, 123, 0.4);
        }

        .date-cell.highlighted {
            background-color: #fff;
            color: var(--dark-blue);
            border-radius: 50%;
            width: 55px;
            height: 55px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            font-weight: 600;
            justify-self: center;
            border: 2px solid var(--purple);
        }

        .date-cell.highlighted.clickable:hover {
            background-color: var(--purple);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(108, 86, 123, 0.3);
        }

        /* Activity display styling */
        .activity-display {
            margin-top: 2rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(108, 86, 123, 0.2);
        }

        .activity-header h3 {
            color: var(--purple);
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .close-activity-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--purple);
            cursor: pointer;
            padding: 0.2rem 0.5rem;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .close-activity-btn:hover {
            background-color: rgba(108, 86, 123, 0.1);
            transform: scale(1.1);
        }

        .activity-list {
            min-height: 100px;
        }

        .activity-item {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            border-left: 4px solid var(--purple);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .activity-item:last-child {
            margin-bottom: 0;
        }

        .activity-time {
            color: var(--purple);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.3rem;
        }

        .activity-name {
            color: var(--dark-blue);
            font-size: 1rem;
            font-weight: 500;
        }

        .no-activities {
            text-align: center;
            color: var(--light-grey);
            font-style: italic;
            padding: 2rem;
            font-size: 1rem;
        }

        /* Enhanced day names */
        .day-name {
            color: var(--purple);
            font-weight: 700;
            font-size: 1rem;
            margin-bottom: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Responsive adjustments for enhanced styling */
        @media (max-width: 480px) {
            .date-cell, .date-cell.highlighted {
                font-size: 1.1rem;
                height: 45px;
                padding: 0.4rem;
            }

            .date-cell.highlighted {
                width: 45px;
                height: 45px;
            }

            .activity-display {
                padding: 1rem;
                margin-top: 1.5rem;
            }

            .activity-header h3 {
                font-size: 1rem;
            }
        }

        @media (max-width: 1024px) {
            .main-calendar-grid {
                gap: 0.4rem 0.8rem;
            }
        }
    </style>

    <!-- Interactive JavaScript Functionality -->
    <script>
        // Sample activity data - in a real application, this would come from a database
        const sampleActivities = {
            '08-01': [
                { time: '09:00', name: 'Rapat Tim Proyek' },
                { time: '14:00', name: 'Presentasi Klien' }
            ],
            '08-05': [
                { time: '10:30', name: 'Training Karyawan Baru' },
                { time: '15:00', name: 'Review Dokumen Teknis' }
            ],
            '08-10': [
                { time: '08:00', name: 'Site Visit - Lokasi A' },
                { time: '13:00', name: 'Lunch Meeting dengan Vendor' }
            ],
            '08-15': [
                { time: '09:30', name: 'Workshop Keselamatan Kerja' }
            ],
            '08-17': [
                { time: '08:00', name: 'Upacara Hari Kemerdekaan' },
                { time: '10:00', name: 'Lomba 17 Agustus' },
                { time: '14:00', name: 'Syukuran Kemerdekaan' }
            ],
            '08-20': [
                { time: '09:00', name: 'Audit Internal' },
                { time: '11:00', name: 'Meeting Evaluasi Bulanan' }
            ],
            '08-25': [
                { time: '08:30', name: 'Inspeksi Peralatan' },
                { time: '10:00', name: 'Koordinasi dengan Subkontraktor' },
                { time: '14:30', name: 'Laporan Progress Mingguan' }
            ],
            '08-30': [
                { time: '09:00', name: 'Closing Meeting Bulan Agustus' }
            ]
        };

        document.addEventListener('DOMContentLoaded', function() {
            const clickableDates = document.querySelectorAll('.date-cell.clickable');
            const activityDisplay = document.getElementById('activityDisplay');
            const selectedDateTitle = document.getElementById('selectedDateTitle');
            const activityList = document.getElementById('activityList');
            const closeActivityBtn = document.getElementById('closeActivityBtn');

            // Add click event listeners to all clickable date cells
            clickableDates.forEach(dateCell => {
                dateCell.addEventListener('click', function() {
                    // Remove previous selection
                    document.querySelectorAll('.date-cell.selected').forEach(cell => {
                        cell.classList.remove('selected');
                    });

                    // Add selection to clicked date
                    this.classList.add('selected');

                    // Get date information
                    const date = this.getAttribute('data-date');
                    const month = this.getAttribute('data-month');
                    const dateKey = `${month}-${date.padStart(2, '0')}`;

                    // Update title
                    const monthNames = {
                        '08': 'Agustus',
                        '07': 'Juli',
                        '09': 'September'
                    };
                    selectedDateTitle.textContent = `Kegiatan untuk ${date} ${monthNames[month] || 'Agustus'} 2025`;

                    // Get activities for this date
                    const activities = sampleActivities[dateKey] || [];

                    // Populate activity list
                    if (activities.length > 0) {
                        activityList.innerHTML = activities.map(activity => `
                            <div class="activity-item">
                                <div class="activity-time">${activity.time}</div>
                                <div class="activity-name">${activity.name}</div>
                            </div>
                        `).join('');
                    } else {
                        activityList.innerHTML = '<div class="no-activities">Tidak ada kegiatan terjadwal untuk tanggal ini</div>';
                    }

                    // Show activity display
                    activityDisplay.style.display = 'block';

                    // Smooth scroll to activity display
                    setTimeout(() => {
                        activityDisplay.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });
                    }, 100);
                });
            });

            // Close activity display
            closeActivityBtn.addEventListener('click', function() {
                activityDisplay.style.display = 'none';
                // Remove selection from all dates
                document.querySelectorAll('.date-cell.selected').forEach(cell => {
                    cell.classList.remove('selected');
                });
            });

            // Add visual feedback for dates with activities
            Object.keys(sampleActivities).forEach(dateKey => {
                const [month, date] = dateKey.split('-');
                const dateCell = document.querySelector(`[data-date="${parseInt(date)}"][data-month="${month}"]`);
                if (dateCell && dateCell.classList.contains('clickable')) {
                    // Add a small indicator for dates with activities
                    const indicator = document.createElement('div');
                    indicator.style.cssText = `
                        position: absolute;
                        top: 5px;
                        right: 5px;
                        width: 6px;
                        height: 6px;
                        background-color: var(--red);
                        border-radius: 50%;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                    `;
                    dateCell.style.position = 'relative';
                    dateCell.appendChild(indicator);
                }
            });
        });
    </script>
</body>
</html>